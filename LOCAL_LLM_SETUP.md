# Local LLM Setup Guide

This guide shows you how to configure Count-On-Me to use local LLMs instead of cloud-based AI services.

## Quick Setup

The application is configured to work with any OpenAI-compatible local LLM server. Here are the most popular options:

## Option 1: LM Studio (Recommended for Beginners)

### Step 1: Install LM Studio
1. Download from [LM Studio](https://lmstudio.ai/)
2. Install and launch the application
3. Download a model (recommended: Llama 3.1 8B, Gemma 2 9B, or similar)

### Step 2: Start Local Server
1. In LM Studio, go to the "Local Server" tab
2. Load your downloaded model
3. Click "Start Server"
4. Note the server URL (usually `http://localhost:1234`)

### Step 3: Configure Count-On-Me
Edit your `.env` file:
```env
AI_PROVIDER=openai
OPENAI_API_KEY=local-api-key
OPENAI_MODEL_NAME=your-model-name
OPENAI_BASE_URL=http://localhost:1234/v1
```

## Option 2: Ollama

### Step 1: Install Ollama
```bash
# Linux/macOS
curl -fsSL https://ollama.ai/install.sh | sh

# Or download from https://ollama.ai/
```

### Step 2: Download and Run a Model
```bash
# Download and run Llama 3.1 8B
ollama run llama3.1:8b

# Or Gemma 2 9B
ollama run gemma2:9b

# List available models
ollama list
```

### Step 3: Start OpenAI-Compatible Server
```bash
# Ollama automatically serves on localhost:11434
# But for OpenAI compatibility, you might need:
ollama serve
```

### Step 4: Configure Count-On-Me
```env
AI_PROVIDER=openai
OPENAI_API_KEY=local-api-key
OPENAI_MODEL_NAME=llama3.1:8b
OPENAI_BASE_URL=http://localhost:11434/v1
```

## Option 3: vLLM (Advanced)

### Step 1: Install vLLM
```bash
pip install vllm
```

### Step 2: Start OpenAI-Compatible Server
```bash
# Example with Llama 3.1 8B
python -m vllm.entrypoints.openai.api_server \
  --model meta-llama/Meta-Llama-3.1-8B-Instruct \
  --port 8000
```

### Step 3: Configure Count-On-Me
```env
AI_PROVIDER=openai
OPENAI_API_KEY=local-api-key
OPENAI_MODEL_NAME=meta-llama/Meta-Llama-3.1-8B-Instruct
OPENAI_BASE_URL=http://localhost:8000/v1
```

## Option 4: Text Generation WebUI (oobabooga)

### Step 1: Install Text Generation WebUI
```bash
git clone https://github.com/oobabooga/text-generation-webui.git
cd text-generation-webui
./start_linux.sh  # or start_windows.bat, start_macos.sh
```

### Step 2: Enable OpenAI API Extension
1. In the web interface, go to "Session" tab
2. Enable "openai" extension
3. Restart with `--extensions openai` flag

### Step 3: Configure Count-On-Me
```env
AI_PROVIDER=openai
OPENAI_API_KEY=local-api-key
OPENAI_MODEL_NAME=your-loaded-model
OPENAI_BASE_URL=http://localhost:5000/v1
```

## ✅ Current Configuration (WORKING!)

Your `.env` file is configured and **successfully connected** to your local LLM:

```env
AI_PROVIDER=openai
OPENAI_API_KEY=local-api-key
OPENAI_MODEL_NAME=gemma-3-12b-it
OPENAI_BASE_URL=http://*************:1234/v1
```

**Confirmed Working Setup:**
- ✅ **Server**: Local LLM server running on `*************:1234`
- ✅ **Model**: Gemma 3 12B Instruct (excellent choice for inventory tasks!)
- ✅ **Connection**: Successfully tested and verified
- ✅ **Available Models**: 44+ models detected on your server
- ✅ **Application**: Count-On-Me is now using your local LLM!

## Testing Your Setup

### Step 1: Test Local Server
```bash
# Test if your local server is responding
curl http://*************:1234/v1/models

# Should return a list of available models
```

### Step 2: Test with Count-On-Me
```bash
# Start the application
./scripts/start.sh

# Check logs for any AI-related errors
tail -f logs/genkit.log
```

## Recommended Models for Inventory Management

### Small Models (4-8GB VRAM)
- **Llama 3.1 8B Instruct** - Excellent reasoning
- **Gemma 2 9B** - Good for structured tasks
- **Phi-3 Medium** - Microsoft's efficient model

### Medium Models (12-16GB VRAM)
- **Llama 3.1 70B** (quantized) - Best performance
- **Gemma 2 27B** - Great for complex tasks
- **Mixtral 8x7B** - Good mixture of experts model

### Large Models (24GB+ VRAM)
- **Llama 3.1 405B** (heavily quantized)
- **Claude 3 Haiku** (if running locally)

## Troubleshooting

### Error: "Connection refused"
- Check if your local LLM server is running
- Verify the IP address and port in `OPENAI_BASE_URL`
- Test with `curl http://your-server:port/v1/models`

### Error: "Model not found"
- Check the exact model name your server exposes
- Use `curl http://your-server:port/v1/models` to see available models
- Update `OPENAI_MODEL_NAME` to match exactly

### Error: "Invalid API key"
- Some local servers require a specific API key
- Try `OPENAI_API_KEY=sk-local` or check your server documentation
- Many local servers accept any non-empty string

### Performance Issues
- Ensure your model fits in VRAM
- Consider using quantized models (Q4, Q5, Q8)
- Adjust context length and batch size in your local server

## Network Configuration

### Local Network Access
If your LLM server is on another machine:
```env
OPENAI_BASE_URL=http://192.168.1.XXX:1234/v1
```

### Docker Setup
If running in Docker:
```env
OPENAI_BASE_URL=http://host.docker.internal:1234/v1
```

### Remote Server
If using a remote server:
```env
OPENAI_BASE_URL=https://your-server.com:1234/v1
```

## Benefits of Local LLMs

✅ **Privacy**: Your data never leaves your network
✅ **Cost**: No per-token charges
✅ **Speed**: No network latency (if local)
✅ **Customization**: Use specialized models
✅ **Offline**: Works without internet
✅ **Control**: Full control over model behavior

## Next Steps

1. **Start your local LLM server** (LM Studio, Ollama, etc.)
2. **Verify the configuration** in your `.env` file
3. **Test the connection** with curl
4. **Start Count-On-Me** with `./scripts/start.sh`
5. **Test AI features** like art recognition and restock recommendations

Your setup should work perfectly with the current configuration!
