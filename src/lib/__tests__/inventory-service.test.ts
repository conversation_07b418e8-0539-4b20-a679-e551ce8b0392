import {
  getInventoryItemsFromStorage,
  saveInventoryItemsToStorage,
  updateInventoryWithRecognizedItems,
} from '../inventory-service'
import { MOCK_INVENTORY_ITEMS } from '../constants'
import type { InventoryItem, RecognizedArtSupply } from '../types'

describe('inventory-service', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    // Clear localStorage before each test
    localStorage.clear()
  })

  describe('getInventoryItemsFromStorage', () => {
    it('should return mock items when localStorage is empty', () => {
      const result = getInventoryItemsFromStorage()

      expect(result).toEqual(
        MOCK_INVENTORY_ITEMS.map(item => ({ ...item, barcode: item.barcode || undefined }))
      )
      expect(localStorage.setItem).toHaveBeenCalledWith(
        'stockpilot-inventory',
        JSON.stringify(MOCK_INVENTORY_ITEMS.map(item => ({ ...item, barcode: item.barcode || undefined })))
      )
    })

    it('should return stored items when localStorage has data', () => {
      const storedItems: InventoryItem[] = [
        {
          id: '1',
          name: 'Test Item',
          quantity: 5,
          price: 10.99,
          barcode: '123456789',
          lastUpdated: '2023-01-01T00:00:00.000Z',
        },
      ]
      localStorage.setItem('stockpilot-inventory', JSON.stringify(storedItems))

      const result = getInventoryItemsFromStorage()

      expect(result).toEqual(storedItems)
      expect(localStorage.getItem).toHaveBeenCalledWith('stockpilot-inventory')
    })

    it('should handle localStorage errors gracefully', () => {
      localStorage.getItem.mockImplementation(() => {
        throw new Error('localStorage error')
      })
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation()

      const result = getInventoryItemsFromStorage()

      expect(result).toEqual(
        MOCK_INVENTORY_ITEMS.map(item => ({ ...item, barcode: item.barcode || undefined }))
      )
      expect(consoleSpy).toHaveBeenCalledWith(
        'Error reading inventory from localStorage:',
        expect.any(Error)
      )

      consoleSpy.mockRestore()
    })

    it('should handle invalid JSON in localStorage', () => {
      localStorage.setItem('stockpilot-inventory', 'invalid json')
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation()

      const result = getInventoryItemsFromStorage()

      expect(result).toEqual(
        MOCK_INVENTORY_ITEMS.map(item => ({ ...item, barcode: item.barcode || undefined }))
      )
      expect(consoleSpy).toHaveBeenCalled()

      consoleSpy.mockRestore()
    })
  })

  describe('saveInventoryItemsToStorage', () => {
    it('should save items to localStorage', () => {
      const items: InventoryItem[] = [
        {
          id: '1',
          name: 'Test Item',
          quantity: 5,
          price: 10.99,
          lastUpdated: '2023-01-01T00:00:00.000Z',
        },
      ]

      saveInventoryItemsToStorage(items)

      expect(localStorage.setItem).toHaveBeenCalledWith(
        'stockpilot-inventory',
        JSON.stringify(items.map(item => ({ ...item, barcode: item.barcode || undefined })))
      )
    })

    // Note: localStorage error handling test is complex due to the isLocalStorageAvailable check
    // The function gracefully handles localStorage unavailability by showing a warning and returning early
  })

  describe('updateInventoryWithRecognizedItems', () => {
    beforeEach(() => {
      // Clear any previous mocks
      jest.clearAllMocks()
      localStorage.clear()

      // Reset localStorage to working state
      const localStorageMock = (() => {
        let store = {}
        return {
          getItem: jest.fn((key) => store[key] || null),
          setItem: jest.fn((key, value) => {
            store[key] = value.toString()
          }),
          removeItem: jest.fn((key) => {
            delete store[key]
          }),
          clear: jest.fn(() => {
            store = {}
          }),
        }
      })()

      Object.defineProperty(window, 'localStorage', {
        value: localStorageMock,
        writable: true,
      })

      // Set up localStorage with a known set of items
      localStorage.setItem('stockpilot-inventory', JSON.stringify([
        {
          id: '1',
          name: 'Wireless Mouse',
          quantity: 25,
          price: 29.99,
          barcode: '789012345678',
          lastUpdated: '2023-01-01T00:00:00.000Z',
        },
      ]))
    })

    it('should update existing item quantity', () => {
      const recognizedItems: RecognizedArtSupply[] = [
        {
          name: 'Wireless Mouse',
          count: 5,
        },
      ]

      const result = updateInventoryWithRecognizedItems(recognizedItems)

      expect(result[0].quantity).toBe(30) // 25 + 5
      expect(result[0].name).toBe('Wireless Mouse')
      expect(localStorage.setItem).toHaveBeenCalled()
    })

    it('should add new item when not found in inventory', () => {
      const recognizedItems: RecognizedArtSupply[] = [
        {
          name: 'New Art Supply',
          count: 3,
          barcode: '987654321',
        },
      ]

      const result = updateInventoryWithRecognizedItems(recognizedItems)

      expect(result).toHaveLength(2)
      const newItem = result.find(item => item.name === 'New Art Supply')
      expect(newItem).toBeDefined()
      expect(newItem?.quantity).toBe(3)
      expect(newItem?.price).toBe(0)
      expect(newItem?.barcode).toBe('987654321')
    })

    it('should ignore items with count <= 0', () => {
      const recognizedItems: RecognizedArtSupply[] = [
        {
          name: 'Invalid Item',
          count: 0,
        },
        {
          name: 'Negative Item',
          count: -1,
        },
      ]

      const result = updateInventoryWithRecognizedItems(recognizedItems)

      expect(result).toHaveLength(1) // Only the original item
      expect(result[0].name).toBe('Wireless Mouse')
    })

    it('should handle case-insensitive name matching', () => {
      const recognizedItems: RecognizedArtSupply[] = [
        {
          name: 'WIRELESS MOUSE',
          count: 2,
        },
      ]

      const result = updateInventoryWithRecognizedItems(recognizedItems)

      expect(result).toHaveLength(1)
      expect(result[0].quantity).toBe(27) // 25 + 2
    })
  })
})
