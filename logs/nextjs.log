
> nextn@0.1.0 dev
> next dev --turbopack -p 9002

   ▲ Next.js 15.2.3 (Turbopack)
   - Local:        http://localhost:9002
   - Network:      http://*************:9002
   - Environments: .env

 ✓ Starting...
 ✓ Ready in 1518ms
 ○ Compiling / ...
 ✓ Compiled / in 3.5s
 GET / 200 in 4348ms
 GET / 200 in 184ms
 ○ Compiling /quick-counter ...
 ✓ Compiled /quick-counter in 2.9s
Using OpenAI provider with model: qwen2-vl-7b-instruct
 GET /quick-counter 200 in 3551ms
 GET /quick-counter 200 in 99ms
Error recognizing art supplies: Error [GenkitError]: NOT_FOUND: Model 'qwen2-vl-7b-instruct' not found
    at async (src/ai/flows/recognize-art-supplies-flow.ts:82:21)
    at async analyzeArtSuppliesImage (src/app/quick-counter/actions.ts:16:31)
  80 |   },
  81 |   async input => {
> 82 |     const {output} = await prompt(input);
     |                     ^
  83 |     if (!output) {
  84 |       throw new Error("AI failed to provide an output.");
  85 |     } {
  source: undefined,
  status: 'NOT_FOUND',
  detail: undefined,
  code: 404,
  originalMessage: "Model 'qwen2-vl-7b-instruct' not found",
  traceId: 'f93c19f5f378524700122c143c49559e'
}
 POST /quick-counter 200 in 975ms
   Reload env: .env
Using OpenAI provider with model: ollama_quantised_llava-next-video-7b-dpo
 GET /quick-counter 200 in 1083ms
Error recognizing art supplies: Error [GenkitError]: NOT_FOUND: Model 'ollama_quantised_llava-next-video-7b-dpo' not found
    at async (src/ai/flows/recognize-art-supplies-flow.ts:82:21)
    at async analyzeArtSuppliesImage (src/app/quick-counter/actions.ts:16:31)
  80 |   },
  81 |   async input => {
> 82 |     const {output} = await prompt(input);
     |                     ^
  83 |     if (!output) {
  84 |       throw new Error("AI failed to provide an output.");
  85 |     } {
  source: undefined,
  status: 'NOT_FOUND',
  detail: undefined,
  code: 404,
  originalMessage: "Model 'ollama_quantised_llava-next-video-7b-dpo' not found",
  traceId: 'd0d5207fbeb8ca6fadc52f87246fe46e'
}
 POST /quick-counter 200 in 907ms
   Reload env: .env
Using OpenAI provider with model: ollama_quantised_llava-next-video-7b-dpo
 GET /quick-counter 200 in 794ms
Error recognizing art supplies: Error [GenkitError]: NOT_FOUND: Model 'ollama_quantised_llava-next-video-7b-dpo' not found
    at async (src/ai/flows/recognize-art-supplies-flow.ts:82:21)
    at async analyzeArtSuppliesImage (src/app/quick-counter/actions.ts:16:31)
  80 |   },
  81 |   async input => {
> 82 |     const {output} = await prompt(input);
     |                     ^
  83 |     if (!output) {
  84 |       throw new Error("AI failed to provide an output.");
  85 |     } {
  source: undefined,
  status: 'NOT_FOUND',
  detail: undefined,
  code: 404,
  originalMessage: "Model 'ollama_quantised_llava-next-video-7b-dpo' not found",
  traceId: '76db06722bc96b504cd83e231299dceb'
}
 POST /quick-counter 200 in 798ms
