import { render, screen } from '@testing-library/react'
import { StatCard } from '../StatCard'

// Mock lucide-react
jest.mock('lucide-react', () => ({
  Archive: () => <svg data-testid="archive-icon" />,
  DollarSign: () => <svg data-testid="dollar-sign-icon" />,
  AlertTriangle: () => <svg data-testid="alert-triangle-icon" />,
}))

const MockIcon = () => <svg data-testid="mock-icon" />

describe('StatCard', () => {
  const defaultProps = {
    title: 'Test Title',
    value: 42,
    icon: MockIcon,
  }

  it('renders title and value correctly', () => {
    render(<StatCard {...defaultProps} />)
    
    expect(screen.getByText('Test Title')).toBeInTheDocument()
    expect(screen.getByText('42')).toBeInTheDocument()
  })

  it('renders string values correctly', () => {
    render(<StatCard {...defaultProps} value="$123.45" />)
    
    expect(screen.getByText('$123.45')).toBeInTheDocument()
  })

  it('renders description when provided', () => {
    const description = 'This is a test description'
    render(<StatCard {...defaultProps} description={description} />)
    
    expect(screen.getByText(description)).toBeInTheDocument()
  })

  it('does not render description when not provided', () => {
    render(<StatCard {...defaultProps} />)
    
    // Check that no paragraph with description class exists
    const descriptions = screen.queryAllByText(/text-xs text-muted-foreground/)
    expect(descriptions).toHaveLength(0)
  })

  it('applies custom className when provided', () => {
    const customClass = 'custom-test-class'
    const { container } = render(<StatCard {...defaultProps} className={customClass} />)
    
    const card = container.firstChild as HTMLElement
    expect(card).toHaveClass(customClass)
  })

  it('renders icon component', () => {
    render(<StatCard {...defaultProps} />)

    // Check that the mock icon is rendered
    const icon = screen.getByTestId('mock-icon')
    expect(icon).toBeInTheDocument()
  })

  it('has correct structure and styling classes', () => {
    render(<StatCard {...defaultProps} />)

    // Check for proper header structure
    const title = screen.getByText('Test Title')
    expect(title).toHaveClass('text-sm', 'font-medium')

    // Check for proper content structure
    const value = screen.getByText('42')
    expect(value).toHaveClass('text-2xl', 'font-bold')
  })

  it('handles zero values correctly', () => {
    render(<StatCard {...defaultProps} value={0} />)
    
    expect(screen.getByText('0')).toBeInTheDocument()
  })

  it('handles negative values correctly', () => {
    render(<StatCard {...defaultProps} value={-5} />)
    
    expect(screen.getByText('-5')).toBeInTheDocument()
  })

  it('handles large numbers correctly', () => {
    render(<StatCard {...defaultProps} value={1000000} />)
    
    expect(screen.getByText('1000000')).toBeInTheDocument()
  })
})
