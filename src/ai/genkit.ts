import {genkit, GenkitError} from 'genkit';
import {googleAI} from '@genkit-ai/googleai';
import {openAI} from 'genkitx-openai';
import {config} from 'dotenv';

config(); // Load environment variables from .env

const provider = process.env.AI_PROVIDER || 'openai'; // Default to OpenAI
let selectedModelName: string;
let plugins: any[];

if (provider === 'openai') {
  const apiKey = process.env.OPENAI_API_KEY;
  if (!apiKey) {
    throw new GenkitError('OPENAI_API_KEY environment variable is not set for OpenAI provider.');
  }
  selectedModelName = process.env.OPENAI_MODEL_NAME || 'gpt-4o-mini';
  const baseURL = process.env.OPENAI_BASE_URL; // Optional custom base URL

  plugins = [openAI({
    apiKey,
    ...(baseURL && { baseURL })
  })];
  console.log(`Using OpenAI provider with model: ${selectedModelName}`);
} else if (provider === 'googleai') {
  const apiKey = process.env.GEMINI_API_KEY;
  if (!apiKey) {
    throw new GenkitError('GEMINI_API_KEY environment variable is not set for Google AI provider.');
  }
  selectedModelName = process.env.GEMINI_MODEL_NAME || 'gemini-1.5-flash-latest';
  plugins = [googleAI({apiKey})];
  console.log(`Using Google AI provider with model: ${selectedModelName}`);
} else {
  throw new GenkitError(
    `Unsupported AI_PROVIDER: ${provider}. Must be 'openai' or 'googleai'.`
  );
}

export const ai = genkit({
  plugins,
  model: selectedModelName, // Set the default model based on the selected provider
});
