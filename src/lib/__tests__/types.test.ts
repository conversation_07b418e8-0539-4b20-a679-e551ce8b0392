import type { 
  InventoryItem, 
  RecognizedArtSupply, 
  RestockRecommendation, 
  GenerateRestockLevelsOutput,
  RecognizeArtSuppliesOutput 
} from '../types'

describe('Types', () => {
  describe('InventoryItem', () => {
    it('should accept valid inventory item', () => {
      const validItem: InventoryItem = {
        id: '1',
        name: 'Test Item',
        quantity: 10,
        price: 29.99,
        barcode: '123456789',
        lastUpdated: '2023-01-01T00:00:00.000Z',
      }

      expect(validItem.id).toBe('1')
      expect(validItem.name).toBe('Test Item')
      expect(validItem.quantity).toBe(10)
      expect(validItem.price).toBe(29.99)
      expect(validItem.barcode).toBe('123456789')
      expect(validItem.lastUpdated).toBe('2023-01-01T00:00:00.000Z')
    })

    it('should accept inventory item without barcode', () => {
      const validItem: InventoryItem = {
        id: '1',
        name: 'Test Item',
        quantity: 10,
        price: 29.99,
        lastUpdated: '2023-01-01T00:00:00.000Z',
      }

      expect(validItem.barcode).toBeUndefined()
    })
  })

  describe('RecognizedArtSupply', () => {
    it('should accept valid recognized art supply', () => {
      const validSupply: RecognizedArtSupply = {
        name: 'Paintbrush - Round Tip',
        count: 5,
        barcode: '987654321',
      }

      expect(validSupply.name).toBe('Paintbrush - Round Tip')
      expect(validSupply.count).toBe(5)
      expect(validSupply.barcode).toBe('987654321')
    })

    it('should accept recognized art supply without barcode', () => {
      const validSupply: RecognizedArtSupply = {
        name: 'Acrylic Paint Tube - Red',
        count: 3,
      }

      expect(validSupply.name).toBe('Acrylic Paint Tube - Red')
      expect(validSupply.count).toBe(3)
      expect(validSupply.barcode).toBeUndefined()
    })
  })

  describe('RestockRecommendation', () => {
    it('should accept valid restock recommendation', () => {
      const validRecommendation: RestockRecommendation = {
        itemId: '1',
        suggestedRestockQuantity: 20,
      }

      expect(validRecommendation.itemId).toBe('1')
      expect(validRecommendation.suggestedRestockQuantity).toBe(20)
    })
  })

  describe('GenerateRestockLevelsOutput', () => {
    it('should accept valid restock levels output', () => {
      const validOutput: GenerateRestockLevelsOutput = {
        restockRecommendations: [
          {
            itemId: '1',
            suggestedRestockQuantity: 20,
          },
          {
            itemId: '2',
            suggestedRestockQuantity: 15,
          },
        ],
        analysisSummary: 'Based on sales data, these items need restocking.',
      }

      expect(validOutput.restockRecommendations).toHaveLength(2)
      expect(validOutput.restockRecommendations[0].itemId).toBe('1')
      expect(validOutput.restockRecommendations[0].suggestedRestockQuantity).toBe(20)
      expect(validOutput.analysisSummary).toBe('Based on sales data, these items need restocking.')
    })

    it('should accept empty recommendations array', () => {
      const validOutput: GenerateRestockLevelsOutput = {
        restockRecommendations: [],
        analysisSummary: 'No restocking needed at this time.',
      }

      expect(validOutput.restockRecommendations).toHaveLength(0)
      expect(validOutput.analysisSummary).toBe('No restocking needed at this time.')
    })
  })

  describe('RecognizeArtSuppliesOutput', () => {
    it('should accept valid art supplies recognition output', () => {
      const validOutput: RecognizeArtSuppliesOutput = {
        recognizedItems: [
          {
            name: 'Paintbrush - Round Tip',
            count: 3,
            barcode: '123456789',
          },
          {
            name: 'Acrylic Paint - Blue',
            count: 2,
          },
        ],
        analysisSummary: 'Detected 2 types of art supplies in the image.',
      }

      expect(validOutput.recognizedItems).toHaveLength(2)
      expect(validOutput.recognizedItems[0].name).toBe('Paintbrush - Round Tip')
      expect(validOutput.recognizedItems[0].count).toBe(3)
      expect(validOutput.recognizedItems[0].barcode).toBe('123456789')
      expect(validOutput.recognizedItems[1].barcode).toBeUndefined()
      expect(validOutput.analysisSummary).toBe('Detected 2 types of art supplies in the image.')
    })

    it('should accept empty recognized items array', () => {
      const validOutput: RecognizeArtSuppliesOutput = {
        recognizedItems: [],
        analysisSummary: 'No art supplies detected in the image.',
      }

      expect(validOutput.recognizedItems).toHaveLength(0)
      expect(validOutput.analysisSummary).toBe('No art supplies detected in the image.')
    })
  })
})
