@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: Arial, Helvetica, sans-serif;
}

@layer base {
  :root {
    --background: 344 78% 87%; /* Light Pink */
    --foreground: 0 0% 10%; /* Dark Gray/Near Black */

    --card: 0 0% 100%; /* White */
    --card-foreground: 0 0% 10%; /* Dark Gray/Near Black */

    --popover: 0 0% 100%; /* White */
    --popover-foreground: 0 0% 10%; /* Dark Gray/Near Black */

    --primary: 176 48% 54%; /* Teal */
    --primary-foreground: 0 0% 100%; /* White */

    --secondary: 344 78% 92%; /* Lighter Pink */
    --secondary-foreground: 0 0% 10%; /* Dark Gray/Near Black */

    --muted: 344 78% 80%; /* Muted Pink */
    --muted-foreground: 0 0% 40%; /* Medium Gray for better contrast on muted pink */

    --accent: 176 48% 45%; /* Darker Teal for accents */
    --accent-foreground: 0 0% 100%; /* White */

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 344 70% 78%; /* Pinkish Border */
    --input: 344 70% 78%;  /* Pinkish Input Border */
    --ring: 176 48% 54%; /* Teal for focus rings */

    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;

    /* Sidebar specific variables */
    --sidebar-background: 0 0% 100%; /* White */
    --sidebar-foreground: 0 0% 10%; /* Dark Gray */
    --sidebar-primary: 176 48% 54%; /* Teal */
    --sidebar-primary-foreground: 0 0% 100%; /* White */
    --sidebar-accent: 344 78% 95%; /* Very Light Pink for hover */
    --sidebar-accent-foreground: 176 48% 45%; /* Teal for text on hover */
    --sidebar-border: 0 0% 90%; /* Light Gray border */
    --sidebar-ring: 176 48% 54%; /* Teal for focus rings */
  }

  .dark {
    /* Keeping dark theme mostly as default, can be customized later if needed */
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 176 48% 54%; /* Teal */
    --primary-foreground: 0 0% 100%; /* White */
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 176 48% 45%; /* Darker Teal */
    --accent-foreground: 0 0% 100%; /* White */
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 176 48% 54%; /* Teal */
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;

    /* Sidebar dark theme variables */
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 176 48% 54%; /* Teal */
    --sidebar-primary-foreground: 0 0% 100%; /* White */
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 176 48% 70%; /* Lighter Teal for dark bg */
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 176 48% 54%; /* Teal */
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
