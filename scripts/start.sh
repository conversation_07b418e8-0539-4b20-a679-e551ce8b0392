#!/bin/bash

# Count-On-Me Application Startup Script
# This script starts both the Next.js application and Genkit AI flows

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check if a port is in use
port_in_use() {
    netstat -tuln 2>/dev/null | grep -q ":$1 " || ss -tuln 2>/dev/null | grep -q ":$1 " || lsof -i :$1 >/dev/null 2>&1
}

# Function to kill process on port
kill_port() {
    local port=$1
    local pid=$(lsof -ti :$port)
    if [ ! -z "$pid" ]; then
        print_warning "Killing existing process on port $port (PID: $pid)"
        kill -9 $pid 2>/dev/null || true
        sleep 2
    fi
}

print_status "Starting Count-On-Me Application..."

# Check if Node.js is installed
if ! command_exists node; then
    print_error "Node.js is not installed. Please install Node.js version 18.x or later."
    exit 1
fi

# Check if npm is installed
if ! command_exists npm; then
    print_error "npm is not installed. Please install npm."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    print_error "Node.js version 18.x or later is required. Current version: $(node --version)"
    exit 1
fi

print_success "Node.js version check passed: $(node --version)"

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    print_error "package.json not found. Please run this script from the project root directory."
    exit 1
fi

# Check if dependencies are installed
if [ ! -d "node_modules" ]; then
    print_status "Installing dependencies..."
    npm install
    print_success "Dependencies installed successfully"
else
    print_status "Dependencies already installed"
fi

# Check for .env file
if [ ! -f ".env" ]; then
    print_warning ".env file not found. Creating a template..."
    cat > .env << EOF
# --- AI Provider Configuration ---
# Set AI_PROVIDER to "googleai" or "openai"
AI_PROVIDER=openai

# --- Google AI Configuration (only needed if AI_PROVIDER is "googleai") ---
GEMINI_API_KEY=YOUR_GEMINI_API_KEY_HERE
# Specify the Gemini model you want to use, e.g., gemini-1.5-flash-latest, gemini-pro
GEMINI_MODEL_NAME=gemini-1.5-flash-latest

# --- OpenAI Configuration (only needed if AI_PROVIDER is "openai") ---
 OPENAI_API_KEY=YOUR_OPENAI_API_KEY_HERE
# Specify the OpenAI model, e.g., gpt-4-turbo-preview, gpt-3.5-turbo
OPENAI_MODEL_NAME=gemma-3-12b-it
# Optional: Specify a custom base URL for OpenAI-compatible APIs
OPENAI_BASE_URL=http://192.168.1.188:1234
EOF
    print_warning "Please edit the .env file with your API keys before running the application."
    print_warning "The application may not work properly without valid API keys."
fi

# Kill any existing processes on the ports we need
print_status "Checking for existing processes..."
kill_port 9002  # Next.js app port
kill_port 4000  # Genkit port (default)

# Create PID file directory
mkdir -p .pids

print_status "Starting Genkit AI flows server..."
# Start Genkit in the background and automatically accept the cookie policy
(echo "" | npm run genkit:watch) > logs/genkit.log 2>&1 &
GENKIT_PID=$!
echo $GENKIT_PID > .pids/genkit.pid
print_success "Genkit server started (PID: $GENKIT_PID)"

# Wait a moment for Genkit to start
sleep 5

print_status "Starting Next.js application..."
# Start Next.js in the background
npm run dev > logs/nextjs.log 2>&1 &
NEXTJS_PID=$!
echo $NEXTJS_PID > .pids/nextjs.pid
print_success "Next.js application started (PID: $NEXTJS_PID)"

# Wait for services to be ready
print_status "Waiting for services to be ready..."
sleep 8

# Check if services are running with retry logic
print_status "Checking if Next.js is ready on port 9002..."
for i in {1..10}; do
    if port_in_use 9002; then
        print_success "Next.js application is running on port 9002"
        break
    elif [ $i -eq 10 ]; then
        print_error "Next.js application failed to start on port 9002 after 10 attempts"
        print_status "Checking logs..."
        if [ -f "logs/nextjs.log" ]; then
            print_status "Last 10 lines of Next.js log:"
            tail -10 logs/nextjs.log
        fi
        exit 1
    else
        print_status "Attempt $i/10: Waiting for Next.js to start..."
        sleep 2
    fi
done

print_success "✅ Count-On-Me application is now running!"
echo ""
print_status "🌐 Next.js Application: http://localhost:9002"
print_status "🤖 Genkit UI (optional): http://localhost:4000"
echo ""

# Test if the application is responding
print_status "Testing application response..."
if curl -s -o /dev/null -w "%{http_code}" http://localhost:9002 | grep -q "200"; then
    print_success "✅ Application is responding correctly!"

    # Try to open browser (optional)
    if command_exists xdg-open; then
        print_status "🌐 Opening application in browser..."
        xdg-open http://localhost:9002 >/dev/null 2>&1 &
    elif command_exists open; then
        print_status "🌐 Opening application in browser..."
        open http://localhost:9002 >/dev/null 2>&1 &
    fi
else
    print_warning "Application may not be fully ready yet. Please check http://localhost:9002 manually."
fi

echo ""
print_status "📋 To stop the application, run: ./scripts/stop.sh"
print_status "📊 To run tests, run: ./scripts/test.sh"
print_status "📝 Logs are available in the logs/ directory"
echo ""
print_warning "Keep this terminal open or the application will stop."
print_status "Press Ctrl+C to stop the application"

# Wait for user interrupt
trap 'print_status "Shutting down..."; ./scripts/stop.sh; exit 0' INT
wait
