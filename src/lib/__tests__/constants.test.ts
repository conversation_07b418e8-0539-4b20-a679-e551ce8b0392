import { LOW_STOCK_THRESHOLD, MOCK_INVENTORY_ITEMS, EXAMPLE_HISTORICAL_SALES } from '../constants'
import type { InventoryItem } from '../types'

describe('constants', () => {
  describe('LOW_STOCK_THRESHOLD', () => {
    it('should be a positive number', () => {
      expect(typeof LOW_STOCK_THRESHOLD).toBe('number')
      expect(LOW_STOCK_THRESHOLD).toBeGreaterThan(0)
    })

    it('should have the expected value', () => {
      expect(LOW_STOCK_THRESHOLD).toBe(10)
    })
  })

  describe('MOCK_INVENTORY_ITEMS', () => {
    it('should be an array of inventory items', () => {
      expect(Array.isArray(MOCK_INVENTORY_ITEMS)).toBe(true)
      expect(MOCK_INVENTORY_ITEMS.length).toBeGreater<PERSON>han(0)
    })

    it('should have valid inventory item structure', () => {
      MOCK_INVENTORY_ITEMS.forEach((item: InventoryItem) => {
        expect(item).toHaveProperty('id')
        expect(item).toHaveProperty('name')
        expect(item).toHaveProperty('quantity')
        expect(item).toHaveProperty('price')
        expect(item).toHaveProperty('lastUpdated')

        expect(typeof item.id).toBe('string')
        expect(typeof item.name).toBe('string')
        expect(typeof item.quantity).toBe('number')
        expect(typeof item.price).toBe('number')
        expect(typeof item.lastUpdated).toBe('string')

        expect(item.id.length).toBeGreaterThan(0)
        expect(item.name.length).toBeGreaterThan(0)
        expect(item.quantity).toBeGreaterThanOrEqual(0)
        expect(item.price).toBeGreaterThanOrEqual(0)

        // Validate ISO date string
        expect(() => new Date(item.lastUpdated)).not.toThrow()
        expect(new Date(item.lastUpdated).toISOString()).toBe(item.lastUpdated)

        // Barcode should be string or undefined
        if (item.barcode !== undefined) {
          expect(typeof item.barcode).toBe('string')
          expect(item.barcode.length).toBeGreaterThan(0)
        }
      })
    })

    it('should have unique IDs', () => {
      const ids = MOCK_INVENTORY_ITEMS.map(item => item.id)
      const uniqueIds = new Set(ids)
      expect(uniqueIds.size).toBe(ids.length)
    })

    it('should contain some items with low stock for testing', () => {
      const lowStockItems = MOCK_INVENTORY_ITEMS.filter(item => item.quantity < LOW_STOCK_THRESHOLD)
      expect(lowStockItems.length).toBeGreaterThan(0)
    })
  })

  describe('EXAMPLE_HISTORICAL_SALES', () => {
    it('should be a valid JSON string', () => {
      expect(typeof EXAMPLE_HISTORICAL_SALES).toBe('string')
      expect(() => JSON.parse(EXAMPLE_HISTORICAL_SALES)).not.toThrow()
    })

    it('should contain valid sales data structure', () => {
      const salesData = JSON.parse(EXAMPLE_HISTORICAL_SALES)
      expect(Array.isArray(salesData)).toBe(true)
      expect(salesData.length).toBeGreaterThan(0)

      salesData.forEach((sale: any) => {
        expect(sale).toHaveProperty('itemId')
        expect(sale).toHaveProperty('salesQuantity')
        expect(sale).toHaveProperty('date')

        expect(typeof sale.itemId).toBe('string')
        expect(typeof sale.salesQuantity).toBe('number')
        expect(typeof sale.date).toBe('string')

        expect(sale.itemId.length).toBeGreaterThan(0)
        expect(sale.salesQuantity).toBeGreaterThan(0)

        // Validate date format (YYYY-MM-DD)
        expect(sale.date).toMatch(/^\d{4}-\d{2}-\d{2}$/)
        expect(() => new Date(sale.date)).not.toThrow()
      })
    })
  })
})
