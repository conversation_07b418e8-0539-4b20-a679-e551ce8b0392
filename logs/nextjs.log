
> nextn@0.1.0 dev
> next dev --turbopack -p 9002

   ▲ Next.js 15.2.3 (Turbopack)
   - Local:        http://localhost:9002
   - Network:      http://*************:9002
   - Environments: .env

 ✓ Starting...
 ✓ Ready in 1567ms
 ○ Compiling / ...
 ✓ Compiled / in 3.4s
 GET / 200 in 4033ms
 GET / 200 in 195ms
 ○ Compiling /quick-counter ...
 ✓ Compiled /quick-counter in 2.9s
Using OpenAI provider with model: unsloth/gemma-3-12b-it
 GET /quick-counter 200 in 3576ms
Error recognizing art supplies: Error [GenkitError]: NOT_FOUND: Model 'unsloth/gemma-3-12b-it' not found
    at async (src/ai/flows/recognize-art-supplies-flow.ts:82:21)
    at async analyzeArtSuppliesImage (src/app/quick-counter/actions.ts:16:31)
  80 |   },
  81 |   async input => {
> 82 |     const {output} = await prompt(input);
     |                     ^
  83 |     if (!output) {
  84 |       throw new Error("AI failed to provide an output.");
  85 |     } {
  source: undefined,
  status: 'NOT_FOUND',
  detail: undefined,
  code: 404,
  originalMessage: "Model 'unsloth/gemma-3-12b-it' not found",
  traceId: 'b49de396951dc87e10f03a625c0385b3'
}
 POST /quick-counter 200 in 1058ms
 GET /quick-counter 200 in 365ms
 ○ Compiling /favicon.ico ...
 ✓ Compiled /favicon.ico in 759ms
 GET /favicon.ico?favicon.56766c03.ico 200 in 886ms
Error recognizing art supplies: Error [GenkitError]: NOT_FOUND: Model 'unsloth/gemma-3-12b-it' not found
    at async (src/ai/flows/recognize-art-supplies-flow.ts:82:21)
    at async analyzeArtSuppliesImage (src/app/quick-counter/actions.ts:16:31)
  80 |   },
  81 |   async input => {
> 82 |     const {output} = await prompt(input);
     |                     ^
  83 |     if (!output) {
  84 |       throw new Error("AI failed to provide an output.");
  85 |     } {
  source: undefined,
  status: 'NOT_FOUND',
  detail: undefined,
  code: 404,
  originalMessage: "Model 'unsloth/gemma-3-12b-it' not found",
  traceId: 'b2f54cb48b49e6df517e00d14b79846c'
}
 POST /quick-counter 200 in 618ms
   Reload env: .env
Using OpenAI provider with model: unsloth/gemma-3-12b-it
 GET /quick-counter 200 in 840ms
 GET /favicon.ico?favicon.56766c03.ico 200 in 42ms
 GET /quick-counter 200 in 468ms
 GET /favicon.ico?favicon.56766c03.ico 200 in 38ms
Error recognizing art supplies: Error [GenkitError]: NOT_FOUND: Model 'unsloth/gemma-3-12b-it' not found
    at async (src/ai/flows/recognize-art-supplies-flow.ts:82:21)
    at async analyzeArtSuppliesImage (src/app/quick-counter/actions.ts:16:31)
  80 |   },
  81 |   async input => {
> 82 |     const {output} = await prompt(input);
     |                     ^
  83 |     if (!output) {
  84 |       throw new Error("AI failed to provide an output.");
  85 |     } {
  source: undefined,
  status: 'NOT_FOUND',
  detail: undefined,
  code: 404,
  originalMessage: "Model 'unsloth/gemma-3-12b-it' not found",
  traceId: 'cf9f14182f9b3ac0b812ccb0ca7e7cbb'
}
 POST /quick-counter 200 in 920ms
   Reload env: .env
Using OpenAI provider with model: unsloth/gemma-3-12b-it
 GET /quick-counter 200 in 849ms
 GET /favicon.ico?favicon.56766c03.ico 200 in 70ms
Error recognizing art supplies: Error [GenkitError]: NOT_FOUND: Model 'unsloth/gemma-3-12b-it' not found
    at async (src/ai/flows/recognize-art-supplies-flow.ts:82:21)
    at async analyzeArtSuppliesImage (src/app/quick-counter/actions.ts:16:31)
  80 |   },
  81 |   async input => {
> 82 |     const {output} = await prompt(input);
     |                     ^
  83 |     if (!output) {
  84 |       throw new Error("AI failed to provide an output.");
  85 |     } {
  source: undefined,
  status: 'NOT_FOUND',
  detail: undefined,
  code: 404,
  originalMessage: "Model 'unsloth/gemma-3-12b-it' not found",
  traceId: '12f3c4bed81815f45a7c6521fecfccf9'
}
 POST /quick-counter 200 in 913ms
 GET /quick-counter 200 in 469ms
 GET /favicon.ico?favicon.56766c03.ico 200 in 40ms
Error recognizing art supplies: Error [GenkitError]: NOT_FOUND: Model 'unsloth/gemma-3-12b-it' not found
    at async (src/ai/flows/recognize-art-supplies-flow.ts:82:21)
    at async analyzeArtSuppliesImage (src/app/quick-counter/actions.ts:16:31)
  80 |   },
  81 |   async input => {
> 82 |     const {output} = await prompt(input);
     |                     ^
  83 |     if (!output) {
  84 |       throw new Error("AI failed to provide an output.");
  85 |     } {
  source: undefined,
  status: 'NOT_FOUND',
  detail: undefined,
  code: 404,
  originalMessage: "Model 'unsloth/gemma-3-12b-it' not found",
  traceId: 'c6bdc2d4d69124dee443b9e83b94be12'
}
 POST /quick-counter 200 in 532ms
