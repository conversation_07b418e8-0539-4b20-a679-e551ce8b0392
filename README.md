
# Count-On-Me - Efficient Inventory Management

"Count-On-Me" is a Next.js application designed for efficient inventory management, particularly for retail stores like art supplies. It features AI-powered tools like "Quick Counter" for recognizing items from images, counting them, and attempting to read barcodes.

## Features

- **Dashboard:** Overview of total items, inventory value, and low stock alerts.
- **Inventory Management:** Add, edit, delete, and search inventory items. Includes support for item names, quantities, prices, and barcodes.
- **Quick Counter (AI-Powered):**
    - Upload an image or use a webcam to capture art supplies.
    - AI identifies items, counts them, and attempts to read barcodes.
    - Users can provide specific instructions to the AI (e.g., "only count red items").
    - Results are displayed in an editable form.
    - Corrected counts and new items can be saved directly to the inventory.
- **Smart Restock Suggestions (AI-Powered):** Input historical sales and current inventory data (JSON format) to get AI-driven restock recommendations.

## Tech Stack

- **Next.js:** React framework for server-side rendering and static site generation.
- **React:** JavaScript library for building user interfaces.
- **TypeScript:** Superset of JavaScript adding static typing.
- **Tailwind CSS:** Utility-first CSS framework for styling.
- **ShadCN UI:** Re-usable UI components.
- **Genkit (with Google Gemini or OpenAI):** AI integration for object recognition, barcode reading attempts, and restock suggestions.
- **Lucide React:** Icon library.

## Getting Started

Follow these instructions to set up and run the project locally.

### Prerequisites

- [Node.js](https://nodejs.org/) (version 18.x or later recommended)
- [npm](https://www.npmjs.com/) (usually comes with Node.js) or [Yarn](https://yarnpkg.com/)

### 1. Clone the Repository (if applicable)

If you have the project files in a Git repository, clone it:

```bash
git clone <your-repository-url>
cd count-on-me 
```

If you downloaded the project as a ZIP, extract it and navigate into the project directory.

### 2. Install Dependencies

Install the necessary project dependencies using npm or yarn:

```bash
npm install
```
or
```bash
yarn install
```

### 3. Set Up Environment Variables

The application is **pre-configured to use OpenAI** with no Google dependencies required.

#### Quick OpenAI Setup (Recommended)

1. Get your API key from [OpenAI Platform](https://platform.openai.com/api-keys)
2. Edit the `.env` file and add your API key:

    ```env
    # --- AI Provider Configuration ---
    AI_PROVIDER=openai

    # --- OpenAI Configuration (recommended - no Google dependencies) ---
    OPENAI_API_KEY=sk-your-actual-api-key-here
    OPENAI_MODEL_NAME=gpt-4o-mini
    ```

#### Alternative: Google AI Configuration

    ```env
    # --- AI Provider Configuration ---
    AI_PROVIDER=googleai

    # --- Google AI Configuration ---
    GEMINI_API_KEY=YOUR_GEMINI_API_KEY_HERE
    GEMINI_MODEL_NAME=gemini-1.5-flash-latest
    ```

For detailed setup instructions, see [OPENAI_SETUP.md](OPENAI_SETUP.md).

### 4. Run the Development Servers

You need to run two development servers simultaneously:
    - The Next.js development server for the frontend application.
    - The Genkit development server for the AI flows.

Open two terminal windows or tabs in your project's root directory.

**Terminal 1: Start the Next.js App**

```bash
npm run dev
```
This will typically start the Next.js application on `http://localhost:9002`.

**Terminal 2: Start the Genkit Flows**

For development with auto-reloading of Genkit flows when you make changes:
```bash
npm run genkit:watch
```
Alternatively, to just start the Genkit server once:
```bash
npm run genkit:dev
```
This will start the Genkit development environment, usually making the flows available locally (Genkit often runs its own UI on `http://localhost:4000` for inspecting flows, but the Next.js app communicates with it programmatically).

### 5. Access the Application

Once both servers are running without errors:

- Open your web browser and navigate to `http://localhost:9002` (or the port Next.js indicates) to use the "Count On Me" application.

## Quick Start (Recommended)

For the easiest setup, use our automated scripts:

```bash
# Start the application (both Next.js and Genkit servers)
./scripts/start.sh

# Run the test suite
./scripts/test.sh

# Stop the application
./scripts/stop.sh
```

The start script will:
- Check system requirements
- Install dependencies if needed
- Create a `.env` template if missing
- Start both servers with proper logging
- Provide helpful status updates

## Available Scripts

### Development Scripts
- `./scripts/start.sh`: Automated startup (starts both servers)
- `./scripts/stop.sh`: Automated shutdown (stops all processes)
- `./scripts/test.sh`: Run test suite with options

### NPM Scripts
- `npm run dev`: Starts the Next.js development server (with Turbopack).
- `npm run genkit:dev`: Starts the Genkit development server for AI flows.
- `npm run genkit:watch`: Starts the Genkit development server with file watching for AI flows.
- `npm run build`: Builds the Next.js application for production.
- `npm run start`: Starts the Next.js production server (after building).
- `npm run lint`: Lints the codebase using Next.js's built-in ESLint configuration.
- `npm run typecheck`: Runs TypeScript to check for type errors.
- `npm run test`: Runs the test suite.
- `npm run test:watch`: Runs tests in watch mode.
- `npm run test:coverage`: Runs tests with coverage reporting.

## Testing

The application includes a comprehensive test suite with 44 tests covering:

### Test Types
- **Unit Tests**: Core business logic and utility functions
- **Component Tests**: UI component rendering and behavior
- **Integration Tests**: Full page functionality and user workflows

### Running Tests

```bash
# Run all tests
./scripts/test.sh

# Run with coverage report
./scripts/test.sh --coverage

# Run in watch mode for development
./scripts/test.sh --watch

# Run specific test types
./scripts/test.sh --unit        # Unit tests only
./scripts/test.sh --component   # Component tests only
./scripts/test.sh --integration # Integration tests only
```

### Test Coverage
- **Inventory Service**: localStorage operations, data management
- **Dashboard Components**: StatCard rendering and calculations
- **Page Integration**: Full dashboard functionality
- **Type Safety**: TypeScript type definitions
- **Constants**: Mock data and configuration validation

## Project Structure (Key Directories)

- `src/app/`: Contains the main application pages and layouts (using Next.js App Router).
- `src/components/`: Shared UI components.
  - `src/components/ui/`: ShadCN UI components.
- `src/lib/`: Utility functions, constants, type definitions, and services (like `inventory-service.ts`).
- `src/ai/`: Genkit related code.
  - `src/ai/flows/`: Genkit AI flows.
- `scripts/`: Development and deployment scripts.
- `logs/`: Application logs (created when running).
- `coverage/`: Test coverage reports (created when running tests with coverage).
- `public/`: Static assets (like images).

## Development

For detailed development information, see [DEVELOPMENT.md](DEVELOPMENT.md).
```