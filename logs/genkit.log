
> nextn@0.1.0 genkit:watch
> genkit start -- tsx --watch src/ai/dev.ts

[Telemetry Server] initialized local file trace store at root: /home/<USER>/ai-stack/Count-On-Me/.genkit/traces
Telemetry API running on http://localhost:4033
Project root: /home/<USER>/ai-stack/Count-On-Me
Genkit Developer UI: http://localhost:4000
Using Google AI provider with model: gemini-1.5-flash-latest
Error reading tools config ENOENT: no such file or directory, open '/home/<USER>/ai-stack/Count-On-Me/.genkit/servers/tools-754570.json'
Error listing actions for googleai
 TypeError: Cannot read properties of undefined (reading 'filter')
    at listActions (/home/<USER>/ai-stack/Count-On-Me/node_modules/@genkit-ai/googleai/src/index.ts:236:8)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async <anonymous> (/home/<USER>/ai-stack/Count-On-Me/node_modules/@genkit-ai/googleai/src/index.ts:291:26)
    at async Object.listActions (/home/<USER>/ai-stack/Count-On-Me/node_modules/genkit/src/plugin.ts:59:16)
    at async Object.listActions (/home/<USER>/ai-stack/Count-On-Me/node_modules/genkit/src/genkit.ts:827:20)
    at async Object.listActions (/home/<USER>/ai-stack/Count-On-Me/node_modules/@genkit-ai/core/src/registry.ts:336:18)
    at async <anonymous> (/home/<USER>/ai-stack/Count-On-Me/node_modules/@genkit-ai/core/src/registry.ts:264:14)
    at async Promise.all (index 0)
    at async Registry.listResolvableActions (/home/<USER>/ai-stack/Count-On-Me/node_modules/@genkit-ai/core/src/registry.ts:260:5)
    at async <anonymous> (/home/<USER>/ai-stack/Count-On-Me/node_modules/@genkit-ai/core/src/reflection.ts:131:25)
Initialized local file dataset store at root: /home/<USER>/ai-stack/Count-On-Me/.genkit/datasets
Initialized local file eval store at root: /home/<USER>/ai-stack/Count-On-Me/.genkit/evals
Error listing actions for googleai
 TypeError: Cannot read properties of undefined (reading 'filter')
    at listActions (/home/<USER>/ai-stack/Count-On-Me/node_modules/@genkit-ai/googleai/src/index.ts:236:8)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async <anonymous> (/home/<USER>/ai-stack/Count-On-Me/node_modules/@genkit-ai/googleai/src/index.ts:291:26)
    at async Object.listActions (/home/<USER>/ai-stack/Count-On-Me/node_modules/genkit/src/plugin.ts:59:16)
    at async Object.listActions (/home/<USER>/ai-stack/Count-On-Me/node_modules/genkit/src/genkit.ts:827:20)
    at async Object.listActions (/home/<USER>/ai-stack/Count-On-Me/node_modules/@genkit-ai/core/src/registry.ts:336:18)
    at async <anonymous> (/home/<USER>/ai-stack/Count-On-Me/node_modules/@genkit-ai/core/src/registry.ts:264:14)
    at async Promise.all (index 0)
    at async Registry.listResolvableActions (/home/<USER>/ai-stack/Count-On-Me/node_modules/@genkit-ai/core/src/registry.ts:260:5)
    at async <anonymous> (/home/<USER>/ai-stack/Count-On-Me/node_modules/@genkit-ai/core/src/reflection.ts:131:25)
Restarting 'src/ai/dev.ts'
Shutting down all Genkit servers...
Using Google AI provider with model: gemini-1.5-flash-latest
Restarting 'src/ai/dev.ts'
Shutting down all Genkit servers...
Using Google AI provider with model: gemini-1.5-flash-latest
Restarting 'src/ai/dev.ts'
Shutting down all Genkit servers...
Using Google AI provider with model: gemini-1.5-flash-latest
