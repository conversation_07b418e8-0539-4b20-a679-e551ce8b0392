#!/bin/bash

# Count-On-Me Application Shutdown Script
# This script stops both the Next.js application and Genkit AI flows

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to kill process by PID
kill_process() {
    local pid=$1
    local name=$2
    
    if [ ! -z "$pid" ] && kill -0 $pid 2>/dev/null; then
        print_status "Stopping $name (PID: $pid)..."
        kill -TERM $pid 2>/dev/null || true
        
        # Wait up to 10 seconds for graceful shutdown
        for i in {1..10}; do
            if ! kill -0 $pid 2>/dev/null; then
                print_success "$name stopped gracefully"
                return 0
            fi
            sleep 1
        done
        
        # Force kill if still running
        print_warning "Force killing $name..."
        kill -9 $pid 2>/dev/null || true
        print_success "$name force stopped"
    else
        print_warning "$name was not running or already stopped"
    fi
}

# Function to kill process on port
kill_port() {
    local port=$1
    local name=$2
    local pids=$(lsof -ti :$port 2>/dev/null || true)
    
    if [ ! -z "$pids" ]; then
        print_status "Killing processes on port $port ($name)..."
        echo $pids | xargs kill -9 2>/dev/null || true
        print_success "Processes on port $port stopped"
    fi
}

print_status "Stopping Count-On-Me Application..."

# Stop processes using PID files
if [ -f ".pids/nextjs.pid" ]; then
    NEXTJS_PID=$(cat .pids/nextjs.pid)
    kill_process $NEXTJS_PID "Next.js application"
    rm -f .pids/nextjs.pid
fi

if [ -f ".pids/genkit.pid" ]; then
    GENKIT_PID=$(cat .pids/genkit.pid)
    kill_process $GENKIT_PID "Genkit server"
    rm -f .pids/genkit.pid
fi

# Also kill any processes on the known ports as backup
kill_port 9002 "Next.js"
kill_port 4000 "Genkit"

# Clean up PID directory
if [ -d ".pids" ]; then
    rmdir .pids 2>/dev/null || true
fi

print_success "✅ Count-On-Me application has been stopped"

# Show any remaining Node.js processes (for debugging)
REMAINING_PROCESSES=$(ps aux | grep -E "(next|genkit|tsx)" | grep -v grep | wc -l)
if [ "$REMAINING_PROCESSES" -gt 0 ]; then
    print_warning "Some Node.js processes may still be running:"
    ps aux | grep -E "(next|genkit|tsx)" | grep -v grep || true
    echo ""
    print_status "If needed, you can kill all Node.js processes with:"
    print_status "pkill -f node"
fi
