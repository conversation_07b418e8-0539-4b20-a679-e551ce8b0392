# Count-On-Me Development Guide

This guide provides detailed information for developers working on the Count-On-Me inventory management application.

## Quick Start

### Using the Startup Scripts (Recommended)

1. **Start the application:**
   ```bash
   ./scripts/start.sh
   ```

2. **Run tests:**
   ```bash
   ./scripts/test.sh
   ```

3. **Stop the application:**
   ```bash
   ./scripts/stop.sh
   ```

### Manual Setup

If you prefer to run commands manually, follow the instructions in the main README.md file.

## Testing

We have a comprehensive test suite covering unit tests, component tests, and integration tests.

### Test Structure

```
src/
├── lib/__tests__/           # Unit tests for utilities and services
│   ├── constants.test.ts    # Tests for constants and mock data
│   ├── inventory-service.test.ts  # Tests for inventory management logic
│   └── types.test.ts        # Tests for TypeScript type definitions
├── components/
│   └── dashboard/__tests__/
│       └── StatCard.test.tsx  # Component tests for UI components
└── app/__tests__/
    └── page.test.tsx        # Integration tests for pages
```

### Running Tests

#### All Tests
```bash
npm test
# or
./scripts/test.sh
```

#### With Coverage
```bash
npm run test:coverage
# or
./scripts/test.sh --coverage
```

#### Watch Mode
```bash
npm run test:watch
# or
./scripts/test.sh --watch
```

#### Specific Test Types
```bash
# Unit tests only
./scripts/test.sh --unit

# Component tests only
./scripts/test.sh --component

# Integration tests only
./scripts/test.sh --integration
```

### Test Coverage

Our test suite covers:

- **Unit Tests (9 tests)**: Core business logic, data validation, and utility functions
- **Component Tests (9 tests)**: UI component rendering and behavior
- **Integration Tests (8 tests)**: Full page functionality and user workflows

**Total: 44 tests** covering the main application functionality.

### Writing New Tests

#### Unit Tests
Place unit tests in `src/lib/__tests__/` for testing:
- Business logic functions
- Data transformation utilities
- Service layer functions
- Type validation

#### Component Tests
Place component tests in `src/components/[component-name]/__tests__/` for testing:
- Component rendering
- User interactions
- Props handling
- State management

#### Integration Tests
Place integration tests in `src/app/__tests__/` for testing:
- Full page functionality
- Data flow between components
- User workflows
- API integration

## Development Scripts

### Available Scripts

| Script | Description |
|--------|-------------|
| `./scripts/start.sh` | Start both Next.js and Genkit servers |
| `./scripts/stop.sh` | Stop all application processes |
| `./scripts/test.sh` | Run the test suite with options |
| `npm run dev` | Start Next.js development server only |
| `npm run genkit:dev` | Start Genkit server only |
| `npm run genkit:watch` | Start Genkit server with file watching |
| `npm run build` | Build the application for production |
| `npm run lint` | Run ESLint |
| `npm run typecheck` | Run TypeScript type checking |

### Script Features

#### Start Script (`./scripts/start.sh`)
- Checks system requirements (Node.js 18+)
- Installs dependencies if needed
- Creates `.env` template if missing
- Kills existing processes on required ports
- Starts both servers with proper logging
- Provides status updates and URLs

#### Stop Script (`./scripts/stop.sh`)
- Gracefully stops all application processes
- Cleans up PID files
- Force kills if graceful shutdown fails
- Shows remaining processes for debugging

#### Test Script (`./scripts/test.sh`)
- Runs TypeScript type checking first
- Runs ESLint for code quality
- Supports multiple test modes (all, unit, component, integration)
- Supports coverage reporting
- Supports watch mode
- Provides helpful debugging tips on failure

## Project Structure

```
Count-On-Me/
├── src/
│   ├── app/                 # Next.js app router pages
│   ├── components/          # Reusable UI components
│   ├── lib/                 # Utilities, services, and types
│   ├── hooks/               # Custom React hooks
│   └── ai/                  # Genkit AI flows
├── scripts/                 # Development scripts
├── logs/                    # Application logs
├── coverage/                # Test coverage reports
├── jest.config.js           # Jest testing configuration
├── jest.setup.js            # Jest setup and mocks
└── .env                     # Environment variables
```

## Environment Setup

### Required Environment Variables

Create a `.env` file in the project root:

```env
# AI Provider Configuration
AI_PROVIDER=googleai  # or "openai"

# Google AI Configuration (if using googleai)
GEMINI_API_KEY=your_gemini_api_key_here
GEMINI_MODEL_NAME=gemini-1.5-flash-latest

# OpenAI Configuration (if using openai)
# OPENAI_API_KEY=your_openai_api_key_here
# OPENAI_MODEL_NAME=gpt-4-turbo-preview
# OPENAI_BASE_URL=  # Optional: for custom endpoints
```

### Development Dependencies

The application uses these key development dependencies:

- **Jest**: Testing framework
- **@testing-library/react**: React component testing utilities
- **@testing-library/jest-dom**: Additional Jest matchers
- **@testing-library/user-event**: User interaction simulation
- **TypeScript**: Type checking and development

## Debugging

### Logs

Application logs are stored in the `logs/` directory:
- `logs/nextjs.log`: Next.js application logs
- `logs/genkit.log`: Genkit AI flows logs

### Common Issues

1. **Port conflicts**: The start script automatically kills processes on ports 9002 and 4000
2. **Missing dependencies**: The start script automatically installs missing dependencies
3. **Environment variables**: The start script creates a template `.env` file if missing
4. **Test failures**: Use `./scripts/test.sh --watch` for interactive debugging

### Debugging Tests

1. **Run specific tests**:
   ```bash
   npm test -- --testNamePattern="specific test name"
   ```

2. **Debug with verbose output**:
   ```bash
   npm test -- --verbose
   ```

3. **Run tests in watch mode**:
   ```bash
   ./scripts/test.sh --watch
   ```

## Contributing

1. **Before committing**: Run `./scripts/test.sh` to ensure all tests pass
2. **Code style**: Follow the existing TypeScript and React patterns
3. **Testing**: Add tests for new features and bug fixes
4. **Documentation**: Update this guide when adding new features or scripts

## Performance

### Build Optimization

The application is configured for optimal performance:
- Next.js with Turbopack for fast development builds
- TypeScript for type safety and better IDE support
- Tailwind CSS for efficient styling
- Jest with proper mocking for fast test execution

### Monitoring

- Use the Genkit UI at `http://localhost:4000` to monitor AI flows
- Check application logs in the `logs/` directory
- Use browser developer tools for frontend debugging
