# 🔧 Local LLM Model Troubleshooting Guide

## ❌ Current Issue: "Failed to load model" Error

You're getting this error because the model exists in your server's model list but **isn't currently loaded/active**.

## 🎯 Quick Fix

### Option 1: Load Model in LM Studio (Recommended)
1. **Open LM Studio**
2. **Go to "Local Server" tab**
3. **Click "Select a model to load"**
4. **Choose one of these models:**
   - `phi-4` (fast, efficient)
   - `lmstudio-community/gemma-3-12b-it` (good for reasoning)
   - `llama-3.2-3b-instruct` (very fast)
5. **Click "Load Model"**
6. **Wait for it to load completely**
7. **Update your `.env` file** with the exact loaded model name

### Option 2: Use Currently Loaded Model
Some LM Studio setups have a default model already loaded. Try these common names:

```env
# Try these one by one in your .env file:
OPENAI_MODEL_NAME=default
OPENAI_MODEL_NAME=current
OPENAI_MODEL_NAME=loaded
```

## 🔍 Find Currently Loaded Model

### Method 1: Test with Simple Request
```bash
# Test if any model responds without specifying name
curl -X POST http://*************:1234/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [{"role": "user", "content": "hi"}],
    "max_tokens": 5
  }'
```

### Method 2: Check LM Studio Interface
1. Open LM Studio
2. Look at the "Local Server" tab
3. See which model shows as "Loaded" or "Active"
4. Use that exact name in your `.env` file

## 📋 Recommended Models for Count-On-Me

Based on your available models, here are the best options:

### 🚀 Fast & Efficient (Recommended for testing)
```env
OPENAI_MODEL_NAME=llama-3.2-3b-instruct
```
- Small, fast, good for inventory tasks
- Should load quickly

### ⚡ Balanced Performance
```env
OPENAI_MODEL_NAME=phi-4
```
- Microsoft's latest efficient model
- Good reasoning capabilities

### 🧠 Best Quality (if you have enough VRAM)
```env
OPENAI_MODEL_NAME=lmstudio-community/gemma-3-12b-it
```
- Excellent for complex reasoning
- Requires more resources

## 🔧 Step-by-Step Fix

### Step 1: Check What's Available
```bash
curl -s http://*************:1234/v1/models | grep '"id"' | head -10
```

### Step 2: Try Loading a Small Model
In LM Studio:
1. Go to "Local Server" tab
2. Select `llama-3.2-3b-instruct` (smallest, fastest)
3. Click "Load Model"
4. Wait for loading to complete

### Step 3: Update Your .env
```env
AI_PROVIDER=openai
OPENAI_API_KEY=local-api-key
OPENAI_MODEL_NAME=llama-3.2-3b-instruct
OPENAI_BASE_URL=http://*************:1234/v1
```

### Step 4: Test the Connection
```bash
node test-local-llm.js
```

### Step 5: Start Count-On-Me
```bash
./scripts/start.sh
```

## 🚨 Common Issues & Solutions

### Issue: "Model not found"
**Solution**: The model name doesn't match exactly
- Check exact spelling and capitalization
- Include any prefixes (like `lmstudio-community/`)
- Try without prefixes if that doesn't work

### Issue: "Connection refused"
**Solution**: LM Studio server isn't running
- Start LM Studio
- Go to "Local Server" tab
- Click "Start Server"

### Issue: "Request timeout"
**Solution**: Model is too large or loading
- Try a smaller model first
- Wait longer for large models to load
- Check available VRAM/RAM

### Issue: "Invalid API key"
**Solution**: Some servers require specific keys
- Try `OPENAI_API_KEY=sk-local`
- Try `OPENAI_API_KEY=lm-studio`
- Check LM Studio settings for required key

## 🎯 Quick Test Commands

### Test 1: Check Server Status
```bash
curl http://*************:1234/v1/models
```

### Test 2: Test Without Model Name
```bash
curl -X POST http://*************:1234/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{"messages":[{"role":"user","content":"hi"}],"max_tokens":5}'
```

### Test 3: Test Specific Model
```bash
curl -X POST http://*************:1234/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{"model":"llama-3.2-3b-instruct","messages":[{"role":"user","content":"hi"}],"max_tokens":5}'
```

## 📱 Next Steps

1. **Load a model in LM Studio** (most important!)
2. **Update your `.env` file** with the exact loaded model name
3. **Test with our script**: `node test-local-llm.js`
4. **Start Count-On-Me**: `./scripts/start.sh`

## 💡 Pro Tips

- **Start small**: Use `llama-3.2-3b-instruct` for initial testing
- **Check resources**: Ensure you have enough VRAM/RAM for larger models
- **Be patient**: First model load can take time
- **Exact names**: Model names must match exactly (case-sensitive)

Your local LLM setup is almost working - you just need to load a model! 🚀
