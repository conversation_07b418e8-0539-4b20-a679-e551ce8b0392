# OpenAI Setup Guide

This guide shows you how to configure Count-On-Me to use OpenAI instead of Google AI.

## Quick Setup (Recommended)

The application is now **pre-configured to use OpenAI by default** with no Google dependencies required.

### Step 1: Get Your OpenAI API Key

1. Go to [OpenAI Platform](https://platform.openai.com/api-keys)
2. Sign in or create an account
3. Click "Create new secret key"
4. Copy your API key (starts with `sk-`)

### Step 2: Configure the Application

1. Open the `.env` file in the project root
2. Replace `YOUR_OPENAI_API_KEY_HERE` with your actual API key:

```env
# --- AI Provider Configuration ---
AI_PROVIDER=openai

# --- OpenAI Configuration ---
OPENAI_API_KEY=sk-your-actual-api-key-here
OPENAI_MODEL_NAME=gpt-4o-mini
```

### Step 3: Start the Application

```bash
./scripts/start.sh
```

That's it! The application will now use OpenAI for all AI features.

## Available OpenAI Models

You can change the model by updating `OPENAI_MODEL_NAME` in your `.env` file:

### Recommended Models:
- **`gpt-4o-mini`** (default) - Fast, cost-effective, good for most tasks
- **`gpt-4o`** - Most capable model, higher cost
- **`gpt-3.5-turbo`** - Fastest, lowest cost

### Example Configuration:
```env
OPENAI_MODEL_NAME=gpt-4o  # Use the most capable model
```

## Custom OpenAI-Compatible APIs

If you're using a custom OpenAI-compatible API (like local models), you can specify a custom base URL:

```env
OPENAI_API_KEY=your-api-key
OPENAI_BASE_URL=http://localhost:1234/v1  # Your custom API endpoint
OPENAI_MODEL_NAME=your-model-name
```

## Troubleshooting

### Error: "OPENAI_API_KEY environment variable is not set"
- Make sure you've added your API key to the `.env` file
- Ensure there are no extra spaces around the API key
- Restart the application after making changes

### Error: "Invalid API key"
- Double-check your API key is correct
- Make sure you have credits available in your OpenAI account
- Verify the API key hasn't expired

### Error: "Model not found"
- Check that the model name is spelled correctly
- Ensure you have access to the specified model
- Try using `gpt-4o-mini` as a fallback

## Cost Considerations

OpenAI charges based on usage. Here are approximate costs (as of 2024):

- **gpt-4o-mini**: ~$0.15 per 1M input tokens, ~$0.60 per 1M output tokens
- **gpt-4o**: ~$5.00 per 1M input tokens, ~$15.00 per 1M output tokens
- **gpt-3.5-turbo**: ~$0.50 per 1M input tokens, ~$1.50 per 1M output tokens

For typical inventory management tasks, costs should be minimal (a few cents per day).

## Switching Back to Google AI (Optional)

If you want to use Google AI instead:

1. Change `AI_PROVIDER=googleai` in your `.env` file
2. Add your `GEMINI_API_KEY`
3. Restart the application

## Features That Use AI

The following features in Count-On-Me use AI:

1. **Art Recognition**: Identify art supplies from photos
2. **Quick Counter**: Count items in images
3. **Restock Recommendations**: AI-powered inventory analysis

All these features will now use OpenAI instead of Google AI.
