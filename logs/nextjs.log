
> nextn@0.1.0 dev
> next dev --turbopack -p 9002

   ▲ Next.js 15.2.3 (Turbopack)
   - Local:        http://localhost:9002
   - Network:      http://*************:9002
   - Environments: .env

 ✓ Starting...
 ✓ Ready in 1577ms
 ○ Compiling / ...
 ✓ Compiled / in 3.4s
 GET / 200 in 4182ms
 GET / 200 in 239ms
 ○ Compiling /quick-counter ...
 ✓ Compiled /quick-counter in 2.8s
Using Google AI provider with model: gemini-1.5-flash-latest
 GET /quick-counter 200 in 3620ms
 GET /quick-counter 200 in 110ms
Error recognizing art supplies: Error [GenkitError]: NOT_FOUND: Model 'gemini-1.5-flash-latest' not found
    at async (src/ai/flows/recognize-art-supplies-flow.ts:82:21)
    at async analyzeArtSuppliesImage (src/app/quick-counter/actions.ts:16:31)
  80 |   },
  81 |   async input => {
> 82 |     const {output} = await prompt(input);
     |                     ^
  83 |     if (!output) {
  84 |       throw new Error("AI failed to provide an output.");
  85 |     } {
  source: undefined,
  status: 'NOT_FOUND',
  detail: undefined,
  code: 404,
  originalMessage: "Model 'gemini-1.5-flash-latest' not found",
  traceId: 'a2eb72af7c865a31a255f6f8a9f6aeee'
}
 POST /quick-counter 200 in 942ms
Error recognizing art supplies: Error [GenkitError]: NOT_FOUND: Model 'gemini-1.5-flash-latest' not found
    at async (src/ai/flows/recognize-art-supplies-flow.ts:82:21)
    at async analyzeArtSuppliesImage (src/app/quick-counter/actions.ts:16:31)
  80 |   },
  81 |   async input => {
> 82 |     const {output} = await prompt(input);
     |                     ^
  83 |     if (!output) {
  84 |       throw new Error("AI failed to provide an output.");
  85 |     } {
  source: undefined,
  status: 'NOT_FOUND',
  detail: undefined,
  code: 404,
  originalMessage: "Model 'gemini-1.5-flash-latest' not found",
  traceId: '6845ae6b8bbc3dfc378899a851107bda'
}
 POST /quick-counter 200 in 729ms
 GET /quick-counter 200 in 277ms
 ○ Compiling /favicon.ico ...
 ✓ Compiled /favicon.ico in 924ms
 GET /favicon.ico?favicon.56766c03.ico 200 in 1109ms
 ✓ Compiled in 26ms
 ✓ Compiled /quick-counter in 54ms
Using Google AI provider with model: gemini-1.5-flash-latest
 GET /quick-counter 200 in 750ms
 GET /favicon.ico?favicon.56766c03.ico 200 in 45ms
 ✓ Compiled in 1064ms
 ✓ Compiled /quick-counter in 207ms
Using Google AI provider with model: gemini-1.5-flash-latest
 GET /quick-counter 200 in 1274ms
 GET /quick-counter 200 in 1818ms
 GET /favicon.ico?favicon.56766c03.ico 200 in 47ms
 ✓ Compiled in 19ms
 ✓ Compiled /quick-counter in 52ms
Using Google AI provider with model: gemini-1.5-flash-latest
 GET /quick-counter 200 in 674ms
 GET /favicon.ico?favicon.56766c03.ico 200 in 33ms
   Reload env: .env
Using OpenAI provider with model: gpt-4o-mini
 GET /quick-counter 200 in 861ms
 GET /favicon.ico?favicon.56766c03.ico 200 in 95ms
Shutting down all Genkit servers...
Shutting down all Genkit servers...
Shutting down all Genkit servers...
Shutting down all Genkit servers...
Shutting down all Genkit servers...
[?25h
