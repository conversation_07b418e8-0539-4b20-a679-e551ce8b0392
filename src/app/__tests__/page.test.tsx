import { render, screen, waitFor } from '@testing-library/react'
import DashboardPage from '../page'
import { MOCK_INVENTORY_ITEMS, LOW_STOCK_THRESHOLD } from '@/lib/constants'
import type { InventoryItem } from '@/lib/types'

// Mock the inventory service
jest.mock('@/lib/inventory-service', () => ({
  getInventoryItemsFromStorage: jest.fn(),
}))

// Mock date-fns format function
jest.mock('date-fns', () => ({
  format: jest.fn((date, formatStr) => {
    if (formatStr === 'PPp') {
      return new Date(date).toLocaleString()
    }
    return new Date(date).toISOString()
  }),
}))

// Mock lucide-react icons
jest.mock('lucide-react', () => ({
  Archive: () => <svg data-testid="archive-icon" />,
  DollarSign: () => <svg data-testid="dollar-sign-icon" />,
  AlertTriangle: () => <svg data-testid="alert-triangle-icon" />,
  ShoppingCart: () => <svg data-testid="shopping-cart-icon" />,
}))

import { getInventoryItemsFromStorage } from '@/lib/inventory-service'

const mockGetInventoryItemsFromStorage = getInventoryItemsFromStorage as jest.MockedFunction<typeof getInventoryItemsFromStorage>

describe('DashboardPage', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders dashboard title', async () => {
    mockGetInventoryItemsFromStorage.mockReturnValue(MOCK_INVENTORY_ITEMS)
    
    render(<DashboardPage />)
    
    expect(screen.getByText('Dashboard')).toBeInTheDocument()
  })

  it('displays correct total items count', async () => {
    const testItems: InventoryItem[] = [
      {
        id: '1',
        name: 'Item 1',
        quantity: 10,
        price: 5.00,
        lastUpdated: new Date().toISOString(),
      },
      {
        id: '2',
        name: 'Item 2',
        quantity: 20,
        price: 10.00,
        lastUpdated: new Date().toISOString(),
      },
    ]
    mockGetInventoryItemsFromStorage.mockReturnValue(testItems)

    render(<DashboardPage />)

    await waitFor(() => {
      expect(screen.getByText('Total Items')).toBeInTheDocument()
      // Check that the value 30 appears in the document
      expect(screen.getByText('30')).toBeInTheDocument() // 10 + 20
    })
  })

  it('calculates and displays correct inventory value', async () => {
    const testItems: InventoryItem[] = [
      {
        id: '1',
        name: 'Item 1',
        quantity: 10,
        price: 5.00,
        lastUpdated: new Date().toISOString(),
      },
      {
        id: '2',
        name: 'Item 2',
        quantity: 5,
        price: 20.00,
        lastUpdated: new Date().toISOString(),
      },
    ]
    mockGetInventoryItemsFromStorage.mockReturnValue(testItems)
    
    render(<DashboardPage />)
    
    await waitFor(() => {
      expect(screen.getByText('Inventory Value')).toBeInTheDocument()
      expect(screen.getByText('$150.00')).toBeInTheDocument() // (10 * 5) + (5 * 20) = 150
    })
  })

  it('identifies and displays low stock items correctly', async () => {
    const testItems: InventoryItem[] = [
      {
        id: '1',
        name: 'Low Stock Item',
        quantity: 5, // Below threshold of 10
        price: 10.00,
        lastUpdated: new Date().toISOString(),
      },
      {
        id: '2',
        name: 'Normal Stock Item',
        quantity: 15, // Above threshold
        price: 20.00,
        lastUpdated: new Date().toISOString(),
      },
    ]
    mockGetInventoryItemsFromStorage.mockReturnValue(testItems)

    render(<DashboardPage />)

    await waitFor(() => {
      expect(screen.getByText('Low Stock Alerts')).toBeInTheDocument()
      expect(screen.getByText('1 item(s) running low')).toBeInTheDocument()
      // Check that the low stock count appears in the document
      const lowStockValues = screen.getAllByText('1')
      expect(lowStockValues.length).toBeGreaterThan(0) // Should find at least one "1"
    })
  })

  it('displays low stock items in the table', async () => {
    const testItems: InventoryItem[] = [
      {
        id: '1',
        name: 'Low Stock Item',
        quantity: 3,
        price: 15.50,
        lastUpdated: '2023-01-01T12:00:00.000Z',
      },
      {
        id: '2',
        name: 'Normal Stock Item',
        quantity: 25,
        price: 10.00,
        lastUpdated: '2023-01-01T12:00:00.000Z',
      },
    ]
    mockGetInventoryItemsFromStorage.mockReturnValue(testItems)
    
    render(<DashboardPage />)
    
    await waitFor(() => {
      // Check table headers
      expect(screen.getByText('Name')).toBeInTheDocument()
      expect(screen.getByText('Quantity')).toBeInTheDocument()
      expect(screen.getByText('Price')).toBeInTheDocument()
      expect(screen.getByText('Last Updated')).toBeInTheDocument()
      
      // Check low stock item is displayed
      expect(screen.getByText('Low Stock Item')).toBeInTheDocument()
      expect(screen.getByText('3')).toBeInTheDocument()
      expect(screen.getByText('$15.50')).toBeInTheDocument()
      
      // Check normal stock item is NOT displayed in low stock table
      expect(screen.queryByText('Normal Stock Item')).not.toBeInTheDocument()
    })
  })

  it('shows message when no low stock items exist', async () => {
    const testItems: InventoryItem[] = [
      {
        id: '1',
        name: 'Normal Stock Item',
        quantity: 25,
        price: 10.00,
        lastUpdated: new Date().toISOString(),
      },
    ]
    mockGetInventoryItemsFromStorage.mockReturnValue(testItems)
    
    render(<DashboardPage />)
    
    await waitFor(() => {
      expect(screen.getByText('No items are currently low on stock. Well done!')).toBeInTheDocument()
    })
  })

  it('handles empty inventory gracefully', async () => {
    mockGetInventoryItemsFromStorage.mockReturnValue([])

    render(<DashboardPage />)

    await waitFor(() => {
      // Check for specific stat cards by their titles
      expect(screen.getByText('Total Items')).toBeInTheDocument()
      expect(screen.getByText('Inventory Value')).toBeInTheDocument()
      expect(screen.getByText('Low Stock Alerts')).toBeInTheDocument()
      expect(screen.getByText('$0.00')).toBeInTheDocument() // Total value
      expect(screen.getByText('No items are currently low on stock. Well done!')).toBeInTheDocument()
    })
  })

  it('applies correct styling for low stock alerts', async () => {
    const testItems: InventoryItem[] = [
      {
        id: '1',
        name: 'Low Stock Item',
        quantity: 5,
        price: 10.00,
        lastUpdated: new Date().toISOString(),
      },
    ]
    mockGetInventoryItemsFromStorage.mockReturnValue(testItems)

    render(<DashboardPage />)

    await waitFor(() => {
      // Just check that the low stock alert shows the correct count
      expect(screen.getByText('1 item(s) running low')).toBeInTheDocument()
      // Check that the low stock count appears in the document
      const lowStockValues = screen.getAllByText('1')
      expect(lowStockValues.length).toBeGreaterThan(0) // Should find at least one "1"
    })
  })
})
