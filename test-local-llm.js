#!/usr/bin/env node

// Simple test script to verify local LLM connection
const https = require('http');

const testModel = async () => {
  const data = JSON.stringify({
    model: "llama-3.2-3b-instruct",
    messages: [
      {
        role: "user",
        content: "Hello! Please respond with just 'Hello back!' to confirm you're working."
      }
    ],
    max_tokens: 20,
    temperature: 0.1
  });

  const options = {
    hostname: '*************',
    port: 1234,
    path: '/v1/chat/completions',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer local-api-key',
      'Content-Length': data.length
    }
  };

  return new Promise((resolve, reject) => {
    console.log('🔍 Testing connection to local LLM...');
    console.log(`📡 Endpoint: http://*************:1234/v1/chat/completions`);
    console.log(`🤖 Model: llama-3.2-3b-instruct`);
    console.log('⏳ Sending request...\n');

    const req = https.request(options, (res) => {
      let responseData = '';

      res.on('data', (chunk) => {
        responseData += chunk;
      });

      res.on('end', () => {
        try {
          const response = JSON.parse(responseData);
          if (response.choices && response.choices[0]) {
            console.log('✅ SUCCESS! Local LLM is working!');
            console.log('📝 Response:', response.choices[0].message.content);
            console.log('🎯 Model used:', response.model || 'phi-4');
            resolve(response);
          } else if (response.error) {
            console.log('❌ ERROR from LLM server:');
            console.log('🔍 Error details:', JSON.stringify(response.error, null, 2));
            reject(new Error(response.error.message || 'Unknown error'));
          } else {
            console.log('⚠️  Unexpected response format:');
            console.log(JSON.stringify(response, null, 2));
            reject(new Error('Unexpected response format'));
          }
        } catch (error) {
          console.log('❌ Failed to parse JSON response:');
          console.log('📄 Raw response:', responseData);
          reject(error);
        }
      });
    });

    req.on('error', (error) => {
      console.log('❌ Connection error:');
      console.log('🔍 Details:', error.message);
      console.log('\n💡 Troubleshooting tips:');
      console.log('   1. Check if your LLM server is running on *************:1234');
      console.log('   2. Verify the model "phi-4" is loaded');
      console.log('   3. Test with: curl http://*************:1234/v1/models');
      reject(error);
    });

    req.on('timeout', () => {
      console.log('⏰ Request timed out (30s)');
      console.log('💡 This might mean:');
      console.log('   - Model is loading (first request can be slow)');
      console.log('   - Model is too large for available resources');
      console.log('   - Server is overloaded');
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.setTimeout(30000); // 30 second timeout
    req.write(data);
    req.end();
  });
};

// Run the test
testModel()
  .then(() => {
    console.log('\n🎉 Local LLM test completed successfully!');
    console.log('✅ Count-On-Me should work with your local LLM setup.');
    process.exit(0);
  })
  .catch((error) => {
    console.log('\n💥 Local LLM test failed!');
    console.log('❌ Error:', error.message);
    console.log('\n🔧 Next steps:');
    console.log('   1. Check your LLM server status');
    console.log('   2. Try a different model name');
    console.log('   3. Check the model list with: curl http://*************:1234/v1/models');
    process.exit(1);
  });
