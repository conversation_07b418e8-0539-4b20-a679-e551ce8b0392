#!/bin/bash

# Count-On-Me Test Runner Script
# This script runs the test suite with proper setup and reporting

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

print_status "Running Count-On-Me Test Suite..."

# Check if Node.js is installed
if ! command_exists node; then
    print_error "Node.js is not installed. Please install Node.js version 18.x or later."
    exit 1
fi

# Check if npm is installed
if ! command_exists npm; then
    print_error "npm is not installed. Please install npm."
    exit 1
fi

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    print_error "package.json not found. Please run this script from the project root directory."
    exit 1
fi

# Check if dependencies are installed
if [ ! -d "node_modules" ]; then
    print_status "Installing dependencies..."
    npm install
    print_success "Dependencies installed successfully"
fi

# Create logs directory if it doesn't exist
mkdir -p logs

# Parse command line arguments
TEST_TYPE="all"
COVERAGE=false
WATCH=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --coverage)
            COVERAGE=true
            shift
            ;;
        --watch)
            WATCH=true
            shift
            ;;
        --unit)
            TEST_TYPE="unit"
            shift
            ;;
        --integration)
            TEST_TYPE="integration"
            shift
            ;;
        --component)
            TEST_TYPE="component"
            shift
            ;;
        -h|--help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --coverage     Generate test coverage report"
            echo "  --watch        Run tests in watch mode"
            echo "  --unit         Run only unit tests"
            echo "  --integration  Run only integration tests"
            echo "  --component    Run only component tests"
            echo "  -h, --help     Show this help message"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Run type checking first (but don't fail on errors in test files)
print_status "Running TypeScript type checking..."
if npm run typecheck; then
    print_success "TypeScript type checking passed"
else
    print_warning "TypeScript type checking found issues (continuing with tests)"
fi

# Run linting
print_status "Running ESLint..."
if npm run lint; then
    print_success "Linting passed"
else
    print_warning "Linting issues found (not blocking tests)"
fi

# Determine which tests to run
case $TEST_TYPE in
    "unit")
        TEST_PATTERN="src/lib/__tests__/"
        print_status "Running unit tests..."
        ;;
    "component")
        TEST_PATTERN="src/components/"
        print_status "Running component tests..."
        ;;
    "integration")
        TEST_PATTERN="src/app/__tests__/"
        print_status "Running integration tests..."
        ;;
    *)
        TEST_PATTERN=""
        print_status "Running all tests..."
        ;;
esac

# Build the test command
if [ "$WATCH" = true ]; then
    TEST_CMD="npm run test:watch"
    if [ ! -z "$TEST_PATTERN" ]; then
        TEST_CMD="$TEST_CMD -- $TEST_PATTERN"
    fi
elif [ "$COVERAGE" = true ]; then
    TEST_CMD="npm run test:coverage"
    if [ ! -z "$TEST_PATTERN" ]; then
        TEST_CMD="$TEST_CMD -- $TEST_PATTERN"
    fi
else
    TEST_CMD="npm test"
    if [ ! -z "$TEST_PATTERN" ]; then
        TEST_CMD="$TEST_CMD -- $TEST_PATTERN"
    fi
fi

# Run the tests
echo ""
print_status "Executing: $TEST_CMD"
echo ""

if eval $TEST_CMD; then
    print_success "✅ All tests passed!"
    
    if [ "$COVERAGE" = true ]; then
        echo ""
        print_status "📊 Coverage report generated in coverage/ directory"
        print_status "📋 Open coverage/lcov-report/index.html in your browser to view detailed coverage"
    fi
    
    echo ""
    print_status "🎉 Test suite completed successfully!"
    exit 0
else
    print_error "❌ Some tests failed!"
    echo ""
    print_status "💡 Tips for debugging:"
    print_status "  - Check the test output above for specific failures"
    print_status "  - Run tests in watch mode: $0 --watch"
    print_status "  - Run specific test types: $0 --unit, $0 --component, or $0 --integration"
    print_status "  - Check logs in the logs/ directory"
    exit 1
fi
